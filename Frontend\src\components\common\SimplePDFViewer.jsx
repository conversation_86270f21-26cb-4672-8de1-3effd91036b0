import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaExternalLinkAlt, FaSync } from 'react-icons/fa';
import '../../styles/SimplePDFViewer.css';

const SimplePDFViewer = ({
  fileUrl,
  title = 'PDF Document',
  className = '',
  height = '100%',
  showDownload = false,
  onDownload = null,
  showFallbackOptions = true
}) => {
  const [isAndroid, setIsAndroid] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    // Detect Android and mobile devices
    const userAgent = navigator.userAgent;
    const androidDevice = /Android/i.test(userAgent);
    const mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
      window.innerWidth <= 768;

    setIsAndroid(androidDevice);
    setIsMobile(mobileDevice);
  }, []);

  // Enhanced PDF URL parameters for better compatibility
  const getPDFUrl = () => {
    if (!fileUrl) return '';

    // Add cache-busting parameter on retry
    const cacheBuster = retryCount > 0 ? `&t=${Date.now()}` : '';

    if (isAndroid) {
      // Android Chrome optimized parameters for inline viewing
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit&embedded=true${cacheBuster}`;
    } else if (isMobile) {
      // iOS and other mobile devices
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit${cacheBuster}`;
    } else {
      // Desktop parameters
      return `${fileUrl}#toolbar=0&navpanes=0&view=FitH${cacheBuster}`;
    }
  };

  // Handle iframe load success
  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    console.log('PDF loaded successfully');
  };

  // Handle iframe load error
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    console.log('PDF preview failed to load');
  };

  // Handle retry
  const handleRetry = () => {
    setHasError(false);
    setIsLoading(true);
    setRetryCount(prev => prev + 1);
  };

  // Handle open in new tab
  const handleOpenInNewTab = () => {
    window.open(fileUrl, '_blank', 'noopener,noreferrer');
  };

  // Handle download (if enabled)
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Fallback download method
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = title || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Show error state with fallback options
  if (hasError && showFallbackOptions) {
    return (
      <div className={`simple-pdf-viewer ${className} simple-pdf-viewer--error`} style={{ height }}>
        <div className="simple-pdf-viewer__error">
          <div className="simple-pdf-viewer__error-icon">
            <FaExclamationTriangle />
          </div>
          <h3>PDF Preview Unavailable</h3>
          <p>Unable to load the PDF preview. Try the options below:</p>

          <div className="simple-pdf-viewer__error-actions">
            <button
              className="simple-pdf-viewer__btn simple-pdf-viewer__btn--primary"
              onClick={handleRetry}
            >
              <FaSync /> Retry Preview
            </button>

            <button
              className="simple-pdf-viewer__btn simple-pdf-viewer__btn--secondary"
              onClick={handleOpenInNewTab}
            >
              <FaExternalLinkAlt /> Open in New Tab
            </button>

            {showDownload && (
              <button
                className="simple-pdf-viewer__btn simple-pdf-viewer__btn--tertiary"
                onClick={handleDownload}
              >
                Download PDF
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Render PDF preview
  return (
    <div className={`simple-pdf-viewer ${className} ${isAndroid ? 'simple-pdf-viewer--android' : ''}`} style={{ height }}>
      {isLoading && (
        <div className="simple-pdf-viewer__loading">
          <div className="simple-pdf-viewer__spinner"></div>
          <p>Loading PDF...</p>
        </div>
      )}

      <div className="simple-pdf-viewer__content">
        <iframe
          key={retryCount} // Force re-render on retry
          src={getPDFUrl()}
          className={`simple-pdf-viewer__iframe ${isAndroid ? 'simple-pdf-viewer__iframe--android' : ''}`}
          title={title}
          loading="lazy"
          onError={handleError}
          onLoad={handleLoad}
          // Enhanced attributes for compatibility
          data-mobile={isMobile ? 'true' : 'false'}
          data-android={isAndroid ? 'true' : 'false'}
          scrolling="yes"
          allowFullScreen={false}
          sandbox="allow-same-origin allow-scripts allow-popups"
          style={{
            pointerEvents: 'auto',
            touchAction: 'pan-x pan-y zoom',
            border: 'none',
            width: '100%',
            height: '100%',
            opacity: isLoading ? 0 : 1,
            transition: 'opacity 0.3s ease'
          }}
        />
      </div>
    </div>
  );
};

export default SimplePDFViewer;
