import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimplePDFViewer from '../SimplePDFViewer';

// Mock console.warn to avoid deprecation warnings in tests
const originalWarn = console.warn;
beforeAll(() => {
  console.warn = jest.fn();
});

afterAll(() => {
  console.warn = originalWarn;
});

describe('SimplePDFViewer', () => {
  const mockS3Url = 'https://bucket.s3.amazonaws.com/file.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256';
  const mockLocalUrl = 'http://localhost:5000/uploads/document.pdf';
  const mockTitle = 'Test Document';

  beforeEach(() => {
    // Mock window.open
    global.open = jest.fn();
    
    // Mock document.createElement for download functionality
    const mockLink = {
      href: '',
      download: '',
      click: jest.fn(),
    };
    document.createElement = jest.fn().mockReturnValue(mockLink);
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders with S3 URL', () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} title={mockTitle} />);
      
      const iframe = screen.getByTitle(mockTitle);
      expect(iframe).toBeInTheDocument();
      expect(iframe).toHaveAttribute('src', expect.stringContaining(mockS3Url));
    });

    test('renders with local URL', () => {
      render(<SimplePDFViewer fileUrl={mockLocalUrl} title={mockTitle} />);
      
      const iframe = screen.getByTitle(mockTitle);
      expect(iframe).toBeInTheDocument();
      expect(iframe).toHaveAttribute('src', expect.stringContaining(mockLocalUrl));
    });

    test('shows loading state initially', () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} />);
      
      expect(screen.getByText('Loading PDF...')).toBeInTheDocument();
    });

    test('applies custom className and height', () => {
      const { container } = render(
        <SimplePDFViewer 
          fileUrl={mockS3Url} 
          className="custom-class" 
          height="500px" 
        />
      );
      
      const viewer = container.firstChild;
      expect(viewer).toHaveClass('simple-pdf-viewer', 'custom-class');
      expect(viewer).toHaveStyle('height: 500px');
    });
  });

  describe('URL Parameter Optimization', () => {
    test('adds desktop parameters for desktop devices', () => {
      // Mock desktop user agent
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        configurable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 1920,
        configurable: true,
      });

      render(<SimplePDFViewer fileUrl={mockLocalUrl} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe.src).toContain('#toolbar=0&navpanes=0&view=FitH');
    });

    test('adds mobile parameters for mobile devices', () => {
      // Mock mobile user agent
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        configurable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
        configurable: true,
      });

      render(<SimplePDFViewer fileUrl={mockLocalUrl} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe.src).toContain('#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit');
    });

    test('adds Android-specific parameters for Android devices', () => {
      // Mock Android user agent
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
        configurable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
        configurable: true,
      });

      render(<SimplePDFViewer fileUrl={mockLocalUrl} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe.src).toContain('#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit&embedded=true');
    });
  });

  describe('Error Handling', () => {
    test('shows error state when iframe fails to load', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      
      // Simulate iframe error
      fireEvent.error(iframe);
      
      await waitFor(() => {
        expect(screen.getByText('PDF Preview Unavailable')).toBeInTheDocument();
        expect(screen.getByText('Unable to load the PDF preview. Try the options below:')).toBeInTheDocument();
      });
    });

    test('shows retry button in error state', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        expect(screen.getByText('Retry Preview')).toBeInTheDocument();
      });
    });

    test('shows open in new tab button in error state', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        expect(screen.getByText('Open in New Tab')).toBeInTheDocument();
      });
    });

    test('shows download button when enabled in error state', async () => {
      render(
        <SimplePDFViewer 
          fileUrl={mockS3Url} 
          showFallbackOptions={true} 
          showDownload={true} 
        />
      );
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        expect(screen.getByText('Download PDF')).toBeInTheDocument();
      });
    });

    test('hides error fallback options when disabled', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={false} />);
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        expect(screen.queryByText('PDF Preview Unavailable')).not.toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    test('handles retry button click', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        const retryButton = screen.getByText('Retry Preview');
        fireEvent.click(retryButton);
      });
      
      // Should show loading state again
      expect(screen.getByText('Loading PDF...')).toBeInTheDocument();
    });

    test('handles open in new tab button click', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        const openButton = screen.getByText('Open in New Tab');
        fireEvent.click(openButton);
      });
      
      expect(global.open).toHaveBeenCalledWith(mockS3Url, '_blank', 'noopener,noreferrer');
    });

    test('handles download button click with custom handler', async () => {
      const mockOnDownload = jest.fn();
      
      render(
        <SimplePDFViewer 
          fileUrl={mockS3Url} 
          showFallbackOptions={true} 
          showDownload={true}
          onDownload={mockOnDownload}
        />
      );
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        const downloadButton = screen.getByText('Download PDF');
        fireEvent.click(downloadButton);
      });
      
      expect(mockOnDownload).toHaveBeenCalled();
    });

    test('handles download button click with default handler', async () => {
      render(
        <SimplePDFViewer 
          fileUrl={mockS3Url} 
          showFallbackOptions={true} 
          showDownload={true}
        />
      );
      
      const iframe = screen.getByRole('presentation');
      fireEvent.error(iframe);
      
      await waitFor(() => {
        const downloadButton = screen.getByText('Download PDF');
        fireEvent.click(downloadButton);
      });
      
      expect(document.createElement).toHaveBeenCalledWith('a');
    });
  });

  describe('Loading States', () => {
    test('hides loading state when iframe loads successfully', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} />);
      
      expect(screen.getByText('Loading PDF...')).toBeInTheDocument();
      
      const iframe = screen.getByRole('presentation');
      fireEvent.load(iframe);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading PDF...')).not.toBeInTheDocument();
      });
    });

    test('applies opacity transition during loading', () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe).toHaveStyle('opacity: 0');
      
      fireEvent.load(iframe);
      
      expect(iframe).toHaveStyle('opacity: 1');
    });
  });

  describe('Accessibility', () => {
    test('has proper iframe attributes', () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} title={mockTitle} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe).toHaveAttribute('title', mockTitle);
      expect(iframe).toHaveAttribute('sandbox', 'allow-same-origin allow-scripts allow-popups');
    });

    test('has proper mobile attributes', () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe).toHaveAttribute('data-mobile');
      expect(iframe).toHaveAttribute('data-android');
    });
  });

  describe('URL Handling', () => {
    test('handles empty fileUrl gracefully', () => {
      render(<SimplePDFViewer fileUrl="" />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe).toHaveAttribute('src', '');
    });

    test('handles null fileUrl gracefully', () => {
      render(<SimplePDFViewer fileUrl={null} />);
      
      const iframe = screen.getByRole('presentation');
      expect(iframe).toHaveAttribute('src', '');
    });

    test('adds cache busting parameter on retry', async () => {
      render(<SimplePDFViewer fileUrl={mockS3Url} showFallbackOptions={true} />);
      
      const iframe = screen.getByRole('presentation');
      const originalSrc = iframe.src;
      
      fireEvent.error(iframe);
      
      await waitFor(() => {
        const retryButton = screen.getByText('Retry Preview');
        fireEvent.click(retryButton);
      });
      
      const newIframe = screen.getByRole('presentation');
      expect(newIframe.src).not.toBe(originalSrc);
      expect(newIframe.src).toContain('&t=');
    });
  });
});
