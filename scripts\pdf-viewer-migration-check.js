#!/usr/bin/env node

/**
 * PDF Viewer Migration Check Script
 * 
 * This script scans the codebase for usage of deprecated PDF viewer components
 * and provides recommendations for migration to SimplePDFViewer.
 */

const fs = require('fs');
const path = require('path');

// Deprecated components to check for
const DEPRECATED_COMPONENTS = [
  'BravePDFViewer',
  'CustomPDFViewer', 
  'UniversalPDFViewer',
  'PDFViewer'
];

// File extensions to scan
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];

// Directories to scan
const SCAN_DIRECTORIES = [
  'Frontend/src/components',
  'Frontend/src/pages',
  'Frontend/src/hooks'
];

class PDFViewerMigrationChecker {
  constructor() {
    this.results = {
      imports: [],
      usages: [],
      recommendations: []
    };
  }

  /**
   * Check if file should be scanned
   */
  shouldScanFile(filePath) {
    const ext = path.extname(filePath);
    return FILE_EXTENSIONS.includes(ext);
  }

  /**
   * Scan file for deprecated PDF viewer usage
   */
  scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const lineNumber = index + 1;
        
        // Check for imports
        DEPRECATED_COMPONENTS.forEach(component => {
          const importRegex = new RegExp(`import\\s+.*${component}.*from`, 'i');
          if (importRegex.test(line)) {
            this.results.imports.push({
              file: filePath,
              line: lineNumber,
              component,
              content: line.trim(),
              type: 'import'
            });
          }
          
          // Check for component usage
          const usageRegex = new RegExp(`<${component}[\\s>]`, 'i');
          if (usageRegex.test(line)) {
            this.results.usages.push({
              file: filePath,
              line: lineNumber,
              component,
              content: line.trim(),
              type: 'usage'
            });
          }
        });
      });
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Recursively scan directory
   */
  scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and other irrelevant directories
          if (!item.startsWith('.') && item !== 'node_modules') {
            this.scanDirectory(fullPath);
          }
        } else if (stat.isFile() && this.shouldScanFile(fullPath)) {
          this.scanFile(fullPath);
        }
      });
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Generate migration recommendations
   */
  generateRecommendations() {
    const fileUsage = new Map();
    
    // Group by file
    [...this.results.imports, ...this.results.usages].forEach(item => {
      if (!fileUsage.has(item.file)) {
        fileUsage.set(item.file, {
          imports: [],
          usages: [],
          components: new Set()
        });
      }
      
      const fileData = fileUsage.get(item.file);
      fileData.components.add(item.component);
      
      if (item.type === 'import') {
        fileData.imports.push(item);
      } else {
        fileData.usages.push(item);
      }
    });

    // Generate recommendations for each file
    fileUsage.forEach((data, filePath) => {
      const recommendation = {
        file: filePath,
        components: Array.from(data.components),
        actions: []
      };

      // Import replacement
      if (data.imports.length > 0) {
        recommendation.actions.push({
          type: 'replace_import',
          description: 'Replace deprecated imports with SimplePDFViewer',
          before: data.imports.map(i => i.content),
          after: "import SimplePDFViewer from './SimplePDFViewer';"
        });
      }

      // Usage replacement
      if (data.usages.length > 0) {
        data.components.forEach(component => {
          recommendation.actions.push({
            type: 'replace_usage',
            description: `Replace <${component}> with <SimplePDFViewer>`,
            component,
            notes: this.getComponentMigrationNotes(component)
          });
        });
      }

      this.results.recommendations.push(recommendation);
    });
  }

  /**
   * Get migration notes for specific component
   */
  getComponentMigrationNotes(component) {
    const notes = {
      'BravePDFViewer': [
        'Remove Brave-specific fallback logic',
        'SimplePDFViewer handles browser compatibility automatically',
        'Update props: remove onError, add showFallbackOptions={true}'
      ],
      'CustomPDFViewer': [
        'Remove react-pdf dependencies if not used elsewhere',
        'SimplePDFViewer provides better mobile compatibility',
        'Update props: remove scale, pageNumber controls'
      ],
      'UniversalPDFViewer': [
        'Remove react-pdf dependencies if not used elsewhere', 
        'SimplePDFViewer has better S3 URL handling',
        'Update props: remove fileName, showNativeOptions'
      ],
      'PDFViewer': [
        'SimplePDFViewer provides better error handling',
        'Enhanced mobile and Android compatibility',
        'Update props: add showFallbackOptions for better UX'
      ]
    };

    return notes[component] || ['Update to SimplePDFViewer for better compatibility'];
  }

  /**
   * Run the migration check
   */
  run() {
    console.log('🔍 Scanning for deprecated PDF viewer components...\n');

    // Scan all specified directories
    SCAN_DIRECTORIES.forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning ${dir}...`);
        this.scanDirectory(dir);
      } else {
        console.warn(`Warning: Directory ${dir} not found`);
      }
    });

    // Generate recommendations
    this.generateRecommendations();

    // Display results
    this.displayResults();
  }

  /**
   * Display scan results
   */
  displayResults() {
    console.log('\n📊 Migration Check Results\n');
    console.log('='.repeat(50));

    // Summary
    const totalIssues = this.results.imports.length + this.results.usages.length;
    console.log(`\n📈 Summary:`);
    console.log(`   • Deprecated imports found: ${this.results.imports.length}`);
    console.log(`   • Deprecated usages found: ${this.results.usages.length}`);
    console.log(`   • Files requiring migration: ${this.results.recommendations.length}`);
    console.log(`   • Total issues: ${totalIssues}`);

    if (totalIssues === 0) {
      console.log('\n✅ No deprecated PDF viewer components found!');
      console.log('   All components are using the recommended SimplePDFViewer.');
      return;
    }

    // Detailed findings
    console.log('\n🔍 Detailed Findings:\n');

    this.results.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec.file}`);
      console.log(`   Components: ${rec.components.join(', ')}`);
      
      rec.actions.forEach(action => {
        console.log(`   
   📝 ${action.description}`);
        
        if (action.before) {
          console.log(`      Before: ${action.before.join(' | ')}`);
          console.log(`      After:  ${action.after}`);
        }
        
        if (action.notes) {
          console.log(`      Notes:`);
          action.notes.forEach(note => {
            console.log(`        • ${note}`);
          });
        }
      });
      
      console.log('');
    });

    // Migration priority
    console.log('\n🎯 Migration Priority:\n');
    console.log('1. HIGH: Files with multiple deprecated components');
    console.log('2. MEDIUM: Files with BravePDFViewer or CustomPDFViewer');
    console.log('3. LOW: Files with UniversalPDFViewer (already updated internally)');

    console.log('\n📚 Next Steps:\n');
    console.log('1. Review the SimplePDFViewer documentation');
    console.log('2. Update imports to use SimplePDFViewer');
    console.log('3. Update component props according to the new API');
    console.log('4. Test PDF viewing with both S3 and local URLs');
    console.log('5. Remove deprecated component files when no longer used');

    console.log('\n📖 Documentation: Frontend/src/components/common/PDF_VIEWERS_CONSOLIDATION.md');
  }
}

// Run the migration check
if (require.main === module) {
  const checker = new PDFViewerMigrationChecker();
  checker.run();
}

module.exports = PDFViewerMigrationChecker;
